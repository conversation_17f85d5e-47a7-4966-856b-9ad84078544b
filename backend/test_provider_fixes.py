#!/usr/bin/env python3
"""
Test script for provider fixes and FlareSolverr integration.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Set environment variables
os.environ["FLARESOLVERR_URL"] = "http://************:8191"

from app.core.providers.registry import ProviderRegistry
from app.core.providers.mangaplus import MangaPlusProvider


async def test_weebcentral_provider():
    """Test WeebCentral provider (replacement for MangaSee)."""
    print("\n" + "=" * 60)
    print("Testing WeebCentral Provider (former MangaSee)")
    print("=" * 60)

    try:
        registry = ProviderRegistry()
        provider = registry.get_provider("weebcentral")

        if not provider:
            print("❌ WeebCentral provider not found")
            return

        # Test search
        print("🔍 Testing search...")
        results, total, has_more = await provider.search("naruto", page=1, limit=5)

        if results:
            print(f"✅ Search successful: {len(results)} results found")
            sample = results[0]
            print(f"📖 Sample result: {sample.title}")
            print(f"🔗 URL: {sample.url}")
        else:
            print("❌ No search results found")

    except Exception as e:
        print(f"❌ WeebCentral test failed: {e}")
        logger.exception("WeebCentral test error")


async def test_mangaplus_provider():
    """Test MangaPlus provider error handling."""
    print("\n" + "=" * 60)
    print("Testing MangaPlus Provider Error Handling")
    print("=" * 60)

    provider = MangaPlusProvider()

    try:
        # Test search (should fail gracefully)
        print("🔍 Testing search (expecting graceful failure)...")
        results, total, has_more = await provider.search("naruto", page=1, limit=5)

        if results:
            print(f"⚠️  Unexpected success: {len(results)} results found")
        else:
            print("✅ Search failed gracefully as expected (API is broken)")

    except Exception as e:
        print(f"❌ MangaPlus test failed unexpectedly: {e}")
        logger.exception("MangaPlus test error")


async def test_provider_registry():
    """Test provider registry with FlareSolverr."""
    print("\n" + "=" * 60)
    print("Testing Provider Registry")
    print("=" * 60)

    try:
        registry = ProviderRegistry()
        providers = registry.get_all_providers()

        print(f"📊 Total providers loaded: {len(providers)}")

        # Test a few providers
        for provider in providers[:3]:  # Test first 3 providers
            print(f"\n🧪 Testing {provider.name}...")
            try:
                results, total, has_more = await provider.search(
                    "test", page=1, limit=2
                )
                if results:
                    print(f"  ✅ {provider.name}: {len(results)} results")
                else:
                    print(f"  ⚠️  {provider.name}: No results")
            except Exception as e:
                print(f"  ❌ {provider.name}: {e}")

    except Exception as e:
        print(f"❌ Provider registry test failed: {e}")
        logger.exception("Provider registry test error")


async def test_flaresolverr_connection():
    """Test direct FlareSolverr connection."""
    print("\n" + "=" * 60)
    print("Testing FlareSolverr Connection")
    print("=" * 60)

    import httpx

    try:
        async with httpx.AsyncClient() as client:
            # Test FlareSolverr health
            response = await client.get("http://************:8191/", timeout=10.0)
            if response.status_code == 200:
                print("✅ FlareSolverr is accessible")

                # Test a simple request
                payload = {
                    "cmd": "request.get",
                    "url": "https://mangasee123.com/",
                    "maxTimeout": 30000,
                }

                response = await client.post(
                    "http://************:8191/v1", json=payload, timeout=60.0
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "ok":
                        print("✅ FlareSolverr can access MangaSee123")
                        print(
                            f"📊 Response length: {len(data['solution']['response'])} characters"
                        )
                    else:
                        print(
                            f"❌ FlareSolverr error: {data.get('message', 'Unknown error')}"
                        )
                else:
                    print(f"❌ FlareSolverr HTTP error: {response.status_code}")
            else:
                print(f"❌ FlareSolverr not accessible: {response.status_code}")

    except Exception as e:
        print(f"❌ FlareSolverr connection test failed: {e}")
        logger.exception("FlareSolverr connection test error")


async def main():
    """Main test function."""
    print("🧪 Testing Provider Fixes and FlareSolverr Integration")
    print("=" * 80)

    # Test FlareSolverr connection first
    await test_flaresolverr_connection()

    # Test individual providers
    await test_weebcentral_provider()
    await test_mangaplus_provider()

    # Test provider registry
    await test_provider_registry()

    print("\n" + "=" * 80)
    print("🎯 Test Summary")
    print("=" * 80)
    print("✅ Tests completed. Check the output above for results.")
    print("💡 If MangaSee works, FlareSolverr integration is successful!")
    print("💡 If MangaPlus fails gracefully, error handling improvements are working!")


if __name__ == "__main__":
    asyncio.run(main())
